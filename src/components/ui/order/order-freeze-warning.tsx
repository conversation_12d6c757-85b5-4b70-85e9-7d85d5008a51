import { AlertTriangle } from 'lucide-react';

import type { OrderEntity, UserType } from '@/core.constants';
import { shouldShowFreezeWarning } from '@/utils/order-utils';

interface OrderFreezeWarningProps {
  order: OrderEntity;
  userType: UserType;
  isFreezed: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function OrderFreezeWarning({
  order,
  userType,
  isFreezed,
  className = '',
  size = 'sm',
}: OrderFreezeWarningProps) {
  if (!shouldShowFreezeWarning(order, userType, isFreezed)) {
    return null;
  }

  const sizeClasses = {
    sm: {
      container: 'p-1.5 text-[10px]',
      icon: 'w-2.5 h-2.5',
    },
    md: {
      container: 'p-3 text-sm',
      icon: 'w-4 h-4',
    },
    lg: {
      container: 'p-4 text-base',
      icon: 'w-5 h-5',
    },
  };

  const classes = sizeClasses[size];

  return (
    <div
      className={`bg-yellow-500/10 border border-yellow-500/20 rounded ${classes.container} ${className}`}
    >
      <div className="flex items-center gap-1">
        <AlertTriangle className={`${classes.icon} text-yellow-400`} />
        <span className="text-yellow-400">Freeze period</span>
      </div>
    </div>
  );
}
