'use client';

import { Spinner } from '@telegram-apps/telegram-ui';
import { Loader2 } from 'lucide-react';
import { forwardRef } from 'react';
import { useLocalStorage } from 'usehooks-ts';

import {
  type CardVariant,
  VirtualizedCard,
} from '@/components/ui/virtualized-card';
import { ItemCacheProvider } from '@/components/ui/virtualized-grid';
import type {
  CollectionEntity,
  OrderEntity,
  UserType,
} from '@/constants/core.constants';
import { LocalStorageKeys } from '@/constants/storage.constants';

interface BaseMarketplaceOrderListProps {
  orders: OrderEntity[];
  loading: boolean;
  loadingMore: boolean;
  emptyMessage: string;
  onOrderClick: (order: OrderEntity) => void;
  gridCols?: string;
}

interface StandardOrderListProps extends BaseMarketplaceOrderListProps {
  variant: 'order' | 'secondary-market';
  collections: CollectionEntity[];
}

interface UserOrderListProps extends BaseMarketplaceOrderListProps {
  variant: 'user-order';
  getUserRole: (order: OrderEntity) => UserType;
}

export type MarketplaceOrderListProps =
  | StandardOrderListProps
  | UserOrderListProps;

export const MarketplaceOrderList = forwardRef<
  HTMLDivElement,
  MarketplaceOrderListProps
>((props, ref) => {
  const {
    orders,
    loading,
    loadingMore,
    emptyMessage,
    onOrderClick,
    variant,
    gridCols = 'grid-cols-2 xl:grid-cols-4',
  } = props;

  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  if (loading) {
    return (
      <div className="text-center py-8">
        <Spinner className="flex justify-center" size="l" />
        <p className="text-[#708499] mt-2">Loading orders...</p>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-[#708499]">{emptyMessage}</p>
      </div>
    );
  }

  const renderCard = (order: OrderEntity, index: number) => {
    const baseProps = {
      key: `${variant}-${order.id}-${index}`,
      order,
      onClick: () => onOrderClick(order),
      index,
      initialRenderedCount: 8,
      variant: variant as CardVariant,
    };

    if (variant === 'user-order') {
      const userOrderProps = props as UserOrderListProps;
      return (
        <VirtualizedCard
          {...baseProps}
          userRole={userOrderProps.getUserRole(order)}
        />
      );
    }

    const standardProps = props as StandardOrderListProps;
    return (
      <VirtualizedCard
        {...baseProps}
        animated={isAnimatedCollection}
        collection={standardProps.collections.find(
          (c) => c.id === order.collectionId,
        )}
      />
    );
  };

  return (
    <>
      <ItemCacheProvider>
        <div className={`grid ${gridCols} gap-2`}>
          {orders.map((order, index) => renderCard(order, index))}
        </div>
      </ItemCacheProvider>

      <div
        ref={ref}
        className="flex justify-center py-4 w-full"
        style={{
          height: '60px',
          minHeight: '60px',
          backgroundColor: 'transparent',
        }}
      >
        {loadingMore && (
          <div className="flex items-center gap-2 text-gray-400">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Loading more orders...</span>
          </div>
        )}
      </div>
    </>
  );
});
