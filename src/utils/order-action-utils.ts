import { toast } from 'sonner';

import {
  makePurchase<PERSON><PERSON><PERSON><PERSON>,
  makePurchaseAsSeller,
  makeSecondaryMarketPurchase,
} from '@/api/orders-api';
import { UserType } from '@/core.constants';

export interface OrderActionResult {
  success: boolean;
  message?: string;
}

export async function executeMarketplaceOrderAction(
  orderId: string,
  userType: UserType,
): Promise<OrderActionResult> {
  try {
    let result;
    if (userType === UserType.BUYER) {
      result = await makePurchaseAsBuyer(orderId);
    } else {
      result = await makePurchaseAsSeller(orderId);
    }

    const message = result.message ?? 'Action completed successfully!';
    toast.success(message);
    return { success: true, message };
  } catch (error: unknown) {
    console.error('Marketplace action failed:', error);
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'Action failed. Please try again.';
    toast.error(errorMessage);
    return { success: false, message: errorMessage };
  }
}

export async function executeSecondaryMarketPurchase(
  orderId: string,
): Promise<OrderActionResult> {
  try {
    const result = await makeSecondaryMarketPurchase(orderId);
    const message =
      result.message ?? 'Secondary market purchase completed successfully!';
    toast.success(message);
    return { success: true, message };
  } catch (error: unknown) {
    console.error('Secondary market purchase failed:', error);
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'Purchase failed. Please try again.';
    toast.error(errorMessage);
    return { success: false, message: errorMessage };
  }
}
