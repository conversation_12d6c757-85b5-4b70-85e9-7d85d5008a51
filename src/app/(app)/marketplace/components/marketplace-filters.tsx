'use client';

import { Input as TgInput, Select } from '@telegram-apps/telegram-ui';
import { useLocalStorage } from 'usehooks-ts';

import { CollectionSelect } from '@/components/ui/collection-select';
import type { CollectionEntity } from '@/constants/core.constants';
import { LocalStorageKeys } from '@/constants/storage.constants';

import type { SortType } from '../hooks/use-marketplace-orders';

interface MarketplaceFiltersProps {
  minPrice: string;
  maxPrice: string;
  selectedCollection: string;
  sortBy: SortType;
  collections: CollectionEntity[];
  onMinPriceChange: (value: string) => void;
  onMaxPriceChange: (value: string) => void;
  onCollectionChange: (value: string) => void;
  onSortChange: (value: SortType) => void;
}

export const MarketplaceFilters = ({
  minPrice,
  maxPrice,
  selectedCollection,
  sortBy,
  collections,
  onMinPriceChange,
  onMaxPriceChange,
  onCollectionChange,
  onSortChange,
}: MarketplaceFiltersProps) => {
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  return (
    <div className="flex flex-wrap items-end gap-2 py-3 rounded-lg">
      <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header="Min"
            placeholder="0"
            value={minPrice}
            onChange={(e) => onMinPriceChange(e.target.value)}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
          />
        </div>
      </div>

      <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header="Max"
            placeholder="0"
            value={maxPrice}
            onChange={(e) => onMaxPriceChange(e.target.value)}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
          />
        </div>
      </div>

      <div className="w-full sm:flex-1 sm:min-w-[120px] sm:w-auto xss:min-w-[140px] mt-2">
        <CollectionSelect
          animated={isAnimatedCollection}
          collections={collections}
          value={selectedCollection}
          onValueChange={onCollectionChange}
          placeholder="All Collections"
        />
      </div>

      <div className="flex-1 min-w-[120px] xss:min-w-[140px] mt-2">
        <div className="[&>div]:p-0!">
          <Select
            header="Sort by"
            value={sortBy}
            onChange={(e) => onSortChange(e.target.value as SortType)}
            className="[&>select]:px-[12px]! [&>select]:py-[6px]! [&+h6]:-top-[12px]!"
          >
            <option value="date_desc">Newest First</option>
            <option value="date_asc">Oldest First</option>
            <option value="price_desc">Price: High to Low</option>
            <option value="price_asc">Price: Low to High</option>
          </Select>
        </div>
      </div>
    </div>
  );
};
