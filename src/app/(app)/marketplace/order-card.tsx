'use client';

import { BaseOrderCard } from '@/components/shared/base-order-card';
import { PriceButton } from '@/components/shared/price-display';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';

interface OrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
  animated?: boolean;
}

export function OrderCard({
  animated,
  order,
  collection,
  onClick,
}: OrderCardProps) {
  return (
    <BaseOrderCard
      animated={animated}
      order={order}
      collection={collection}
      onClick={onClick}
    >
      <PriceButton amount={order.amount} />
    </BaseOrderCard>
  );
}
