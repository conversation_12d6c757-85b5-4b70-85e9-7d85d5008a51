'use client';

import { useEffect, useState } from 'react';

import { MarketplaceFilters } from '@/components/shared/marketplace-filters';
import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import {
  CachePatterns,
  type OrderEntity,
  UserType,
} from '@/constants/core.constants';
import { useAppCache } from '@/contexts/AppCacheContext';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useMarketplaceFilters } from '@/hooks/use-marketplace-filters';
import { useRootContext } from '@/root-context';

import { MarketplaceHeader } from './marketplace/components/marketplace-header';
import { MarketplaceTabs } from './marketplace/components/marketplace-tabs';
import { CreateOrderDrawer } from './marketplace/create-order-drawer';
import type { TabType } from './marketplace/hooks/use-marketplace-orders';
import { useMarketplaceOrders } from './marketplace/hooks/use-marketplace-orders';
import { OrderDetailsDrawer } from './marketplace/order-details-drawer';

export default function MarketplacePage() {
  const { collections, refetchUser, currentUser } = useRootContext();
  const cache = useAppCache();
  const [activeTab, setActiveTab] = useState<TabType>('sellers');
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);

  const filters = useMarketplaceFilters();
  const ordersFilters = {
    ...filters.getFilters(),
    currentUserId: currentUser?.id,
  };

  const { sellersState, buyersState, loadOrders, loadMoreOrders } =
    useMarketplaceOrders({
      activeTab,
      filters: ordersFilters,
    });

  const sellersLoadMoreRef = useInfiniteScroll({
    hasMore: sellersState.hasMore,
    loading: sellersState.loading || sellersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'sellers') {
        loadMoreOrders();
      }
    },
  });

  const buyersLoadMoreRef = useInfiniteScroll({
    hasMore: buyersState.hasMore,
    loading: buyersState.loading || buyersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'buyers') {
        loadMoreOrders();
      }
    },
  });

  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  const handleCreateOrder = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleOrderCreated = () => {
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_BUYERS);
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_SELLERS);
    cache.invalidatePattern(CachePatterns.SECONDARY_MARKET_ORDERS);
    loadOrders(true);
    refetchUser();
  };

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = handleOrderCreated;

  return (
    <div className="space-y-2 bg-[#17212b] min-h-screen">
      <MarketplaceHeader onCreateOrder={handleCreateOrder} />

      <Tabs value={activeTab}>
        <MarketplaceTabs activeTab={activeTab} onTabChange={setActiveTab} />

        <MarketplaceFilters
          minPrice={filters.minPrice}
          maxPrice={filters.maxPrice}
          selectedCollection={filters.selectedCollection}
          sortBy={filters.sortBy}
          collections={collections}
          onMinPriceChange={filters.setMinPrice}
          onMaxPriceChange={filters.setMaxPrice}
          onCollectionChange={filters.setSelectedCollection}
          onSortChange={filters.setSortBy}
        />

        <TabsContent value="sellers" className="space-y-4">
          <MarketplaceOrderList
            variant="order"
            orders={sellersState.orders}
            collections={collections}
            loading={sellersState.loading}
            loadingMore={sellersState.loadingMore}
            emptyMessage="No orders found for sellers"
            onOrderClick={handleOrderClick}
            ref={sellersLoadMoreRef}
          />
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <MarketplaceOrderList
            variant="order"
            orders={buyersState.orders}
            collections={collections}
            loading={buyersState.loading}
            loadingMore={buyersState.loadingMore}
            emptyMessage="No orders found for buyers"
            onOrderClick={handleOrderClick}
            ref={buyersLoadMoreRef}
          />
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        userType={activeTab === 'sellers' ? UserType.SELLER : UserType.BUYER}
        onOrderCreated={handleOrderCreated}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={activeTab === 'sellers' ? UserType.SELLER : UserType.BUYER}
        onOrderAction={handleOrderAction}
      />
    </div>
  );
}
