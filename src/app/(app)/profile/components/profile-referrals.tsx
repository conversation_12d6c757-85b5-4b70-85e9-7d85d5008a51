'use client';

import { useEffect, useState } from 'react';

import { getUserReferrals } from '@/api/user-api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { UserEntity } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

export const ProfileReferrals = () => {
  const { currentUser } = useRootContext();
  const [referrals, setReferrals] = useState<UserEntity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReferrals = async () => {
      if (!currentUser?.id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const userReferrals = await getUserReferrals(currentUser.id);
        setReferrals(userReferrals);
      } catch (err) {
        console.error('Error fetching referrals:', err);
        setError('Failed to load referrals');
      } finally {
        setIsLoading(false);
      }
    };

    fetchReferrals();
  }, [currentUser?.id]);

  if (!currentUser) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Your Referrals ({referrals.length})</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <p className="text-[#708499]">Loading referrals...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-400">{error}</p>
          </div>
        ) : referrals.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-[#708499]">No referrals yet</p>
            <p className="text-xs text-[#708499] mt-2">
              Share your referral link to start earning rewards!
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Referral Fee</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {referrals.map((referral) => (
                  <TableRow key={referral.id}>
                    <TableCell>
                      {referral.displayName || referral.name || 'Anonymous'}
                    </TableCell>
                    <TableCell>
                      {referral.referral_fee
                        ? `${(referral.referral_fee / 100).toFixed(2)}%`
                        : 'Default'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
