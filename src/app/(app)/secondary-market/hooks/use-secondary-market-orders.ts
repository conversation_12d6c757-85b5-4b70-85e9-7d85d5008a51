import { useCallback } from 'react';

import { getSecondaryMarketOrders } from '@/api/orders-api';
import type { SortType } from '@/components/shared/marketplace-filters';
import { useOrderLoading } from '@/hooks/use-order-loading';

interface SecondaryMarketFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy: SortType;
  currentUserId?: string;
}

interface UseSecondaryMarketOrdersProps {
  filters: SecondaryMarketFilters;
}

export const useSecondaryMarketOrders = ({
  filters,
}: UseSecondaryMarketOrdersProps) => {
  const loadOrdersFunction = useCallback(
    async (
      requestFilters: SecondaryMarketFilters & {
        lastDoc?: unknown;
        limit?: number;
      },
    ) => {
      return await getSecondaryMarketOrders(requestFilters);
    },
    [],
  );

  return useOrderLoading({
    loadOrdersFunction,
    filters,
  });
};
