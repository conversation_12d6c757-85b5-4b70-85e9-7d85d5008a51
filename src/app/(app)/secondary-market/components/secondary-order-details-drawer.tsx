'use client';

import { useState } from 'react';

import {
  OrderDetailsActionButtons,
  OrderDetailsBaseDrawer,
  OrderDetailsDescriptionSection,
  OrderDetailsHeaderSection,
  OrderDetailsImageSection,
  OrderDetailsInfoRow,
  OrderDetailsUserInfoSection,
} from '@/app/(app)/marketplace/order-details-drawer/index';
import { TonLogo } from '@/components/TonLogo';
import { Badge } from '@/components/ui/badge';
import type { OrderEntity } from '@/constants/core.constants';
import { useOrderUserInfo } from '@/hooks/use-order-user-info';
import { useRootContext } from '@/root-context';
import { executeSecondaryMarketPurchase } from '@/utils/order-action-utils';

interface SecondaryOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderAction?: () => void;
}

export function SecondaryOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  onOrderAction,
}: SecondaryOrderDetailsDrawerProps) {
  const { collections } = useRootContext();
  const [actionLoading, setActionLoading] = useState(false);

  const collection = order
    ? collections.find((c) => c.id === order.collectionId) || null
    : null;

  const {
    userInfo: sellerInfo,
    loading,
    handleClose,
  } = useOrderUserInfo({
    userId: order?.sellerId,
    isOpen: open,
  });

  const handlePurchase = async () => {
    if (!order?.id) return;

    setActionLoading(true);
    const result = await executeSecondaryMarketPurchase(order.id);

    if (result.success) {
      onOpenChange(false);
      if (onOrderAction) {
        onOrderAction();
      }
    }
    setActionLoading(false);
  };

  const handleDrawerClose = () => {
    handleClose();
    onOpenChange(false);
  };

  if (!order) return null;

  const actionLabel = (
    <>
      Buy &#40;{order.secondaryMarketPrice || 0}{' '}
      <TonLogo className="-m-2" size={18} />
      <span className="-ml-1 translate-x-[1px]">&#41;</span>
    </>
  );

  return (
    <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
      <OrderDetailsImageSection
        collectionId={order.collectionId}
        collection={collection}
      />

      <OrderDetailsHeaderSection order={order} collection={collection} />

      <div className="space-y-4">
        <OrderDetailsDescriptionSection collection={collection} />

        <div className="space-y-3">
          <OrderDetailsInfoRow
            label="Primary Price"
            value={
              <div className="flex items-center gap-1">
                <span>{order.amount}</span>
                <TonLogo size={16} />
              </div>
            }
          />

          <OrderDetailsInfoRow
            label="Secondary Price"
            value={
              <div className="flex items-center gap-1">
                <span>{order.secondaryMarketPrice || 0}</span>
                <TonLogo size={16} className="text-[#6ab2f2]" />
              </div>
            }
          />

          <OrderDetailsInfoRow
            label="Status"
            value={
              <Badge
                variant="secondary"
                className="bg-[#6ab2f2]/20 text-[#6ab2f2] border-[#6ab2f2]/30"
              >
                Secondary Market
              </Badge>
            }
          />

          <OrderDetailsInfoRow
            label="Market Type"
            value={<span>Resale</span>}
            isLast
          />
        </div>
      </div>

      {order.sellerId && (
        <OrderDetailsUserInfoSection
          userInfo={sellerInfo}
          loading={loading}
          userLabel="Current Owner"
        />
      )}

      <OrderDetailsActionButtons
        primaryAction={{
          label: actionLabel,
          onClick: handlePurchase,
          loading: actionLoading,
        }}
        onClose={handleDrawerClose}
        actionLoading={actionLoading}
      />
    </OrderDetailsBaseDrawer>
  );
}
