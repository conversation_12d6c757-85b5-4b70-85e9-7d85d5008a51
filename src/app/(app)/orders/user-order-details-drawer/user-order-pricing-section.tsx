import { TonLogo } from '@/components/TonLogo';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { hasSecondaryMarketPrice } from '@/utils/order-utils';

interface UserOrderPricingSectionProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
}

export function UserOrderPricingSection({
  order,
  collection,
}: UserOrderPricingSectionProps) {
  const hasSecondaryPrice = hasSecondaryMarketPrice(order);

  return (
    <div className="text-center space-y-3">
      <h1 className="text-2xl font-bold text-[#f5f5f5]">
        {collection?.name || `Collection ${order.collectionId}`}
      </h1>

      {hasSecondaryPrice ? (
        <div className="space-y-3">
          <div>
            <p className="text-[#708499] text-sm mb-1">Primary Price</p>
            <div className="flex items-center justify-center gap-2 p-2 bg-[#232e3c] rounded-lg">
              <TonLogo size={20} />
              <span className="text-xl font-semibold text-[#f5f5f5]">
                {order.amount}
              </span>
              <span className="text-sm text-[#708499]">TON</span>
            </div>
          </div>

          <div>
            <p className="text-[#6ab2f2] text-sm mb-1">
              Secondary Market Price
            </p>
            <div className="flex items-center justify-center gap-2 p-3 bg-[#17212b] rounded-lg border border-[#6ab2f2]/20">
              <TonLogo size={24} />
              <span className="text-3xl font-bold text-[#6ab2f2]">
                {order.secondaryMarketPrice}
              </span>
              <span className="text-lg text-[#708499]">TON</span>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center gap-2 p-3 bg-[#17212b] rounded-lg">
          <TonLogo size={24} />
          <span className="text-3xl font-bold text-[#f5f5f5]">
            {order.amount}
          </span>
          <span className="text-lg text-[#708499]">TON</span>
        </div>
      )}
    </div>
  );
}
