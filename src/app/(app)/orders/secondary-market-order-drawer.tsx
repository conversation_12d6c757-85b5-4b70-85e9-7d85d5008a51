'use client';

import { <PERSON><PERSON><PERSON><PERSON>gle, Loader2, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

import { getAppConfig } from '@/api/app-config-api';
import { setSecondaryMarketPrice } from '@/api/order-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import type { OrderEntity } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

interface SecondaryMarketOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderCreated: (updatedPrice?: number) => void;
}

export function SecondaryMarketOrderDrawer({
  open,
  onOpenChange,
  order,
  onOrderCreated,
}: SecondaryMarketOrderDrawerProps) {
  const { currentUser } = useRootContext();
  const [price, setPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [minPrice, setMinPrice] = useState<number>(1);

  useEffect(() => {
    const loadMinPrice = async () => {
      try {
        const feesConfig = await getAppConfig();
        if (feesConfig?.min_secondary_market_price) {
          setMinPrice(feesConfig.min_secondary_market_price);
        }
      } catch (error) {
        console.error('Error loading min secondary market price:', error);
      }
    };

    if (open) {
      loadMinPrice();
      // Pre-populate with existing secondary market price if it exists, otherwise reset
      if (order?.secondaryMarketPrice && order.secondaryMarketPrice > 0) {
        setPrice(order.secondaryMarketPrice.toString());
      } else {
        setPrice('');
      }
    }
  }, [open, order?.secondaryMarketPrice]);

  const priceValue = parseFloat(price);
  const isValidPrice = !isNaN(priceValue) && priceValue >= minPrice;
  const isUpdating =
    order?.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  const handleCreateOrder = async () => {
    if (!order || !currentUser || !isValidPrice) return;

    setLoading(true);
    try {
      const result = await setSecondaryMarketPrice(order.id!, priceValue);

      if (result.success) {
        toast.success(
          isUpdating
            ? 'Secondary market order updated successfully!'
            : 'Secondary market order created successfully!',
        );
        onOrderCreated(priceValue);
        onOpenChange(false);
      } else {
        toast.error(
          result.message || 'Failed to create secondary market order',
        );
      }
    } catch (error: unknown) {
      console.error('Error creating secondary market order:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to create secondary market order';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (!order) return null;

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[60]" />
        <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] h-[70vh] mt-24 fixed bottom-0 left-0 right-0 z-[60]">
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="text-center mb-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <TrendingUp className="w-6 h-6 text-[#6ab2f2]" />
                <h2 className="text-xl font-bold text-[#f5f5f5]">
                  {isUpdating ? 'Update Resale Order' : 'Create Resale Order'}
                </h2>
              </div>
              <p className="text-[#708499] text-sm">
                {isUpdating
                  ? 'Update your price for reselling this order on the secondary market'
                  : 'Set your price for reselling this order on the secondary market'}
              </p>
            </div>

            <div className="bg-[#232e3c] rounded-2xl p-4 mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-[#708499] text-sm">
                  Original Order Price
                </span>
                <span className="text-[#f5f5f5] font-semibold">
                  {order.amount} TON
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-[#708499] text-sm">Order Number</span>
                <span className="text-[#6ab2f2] font-semibold">
                  #{order.number || order.id?.slice(-6)}
                </span>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-[#f5f5f5] font-medium mb-2">
                  Resale Price (TON)
                </label>
                <Input
                  type="number"
                  step="0.01"
                  min={minPrice}
                  placeholder={`Minimum ${minPrice} TON`}
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="bg-[#232e3c] border-[#3a4a5c] text-[#f5f5f5] placeholder-[#708499] rounded-xl h-12"
                />
                <div className="flex justify-between items-center mt-2 text-sm">
                  <span className="text-[#708499]">
                    Minimum: {minPrice} TON
                  </span>
                  {priceValue > 0 && (
                    <span
                      className={`font-medium ${isValidPrice ? 'text-green-400' : 'text-red-400'}`}
                    >
                      {isValidPrice ? '✓ Valid' : '✗ Too low'}
                    </span>
                  )}
                </div>
              </div>

              <div className="bg-orange-500/10 border border-orange-500/20 rounded-2xl p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-orange-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-orange-400 font-semibold text-sm mb-1">
                      Important
                    </p>
                    <p className="text-[#708499] text-sm">
                      Once you create a resale order, other users can purchase
                      it at your set price. You&apos;ll receive the payment
                      minus marketplace fees.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1 rounded-xl py-3 font-semibold border-[#3a4a5c] text-[#708499] hover:bg-[#232e3c]"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateOrder}
                disabled={!isValidPrice || loading}
                className="flex-1 rounded-xl py-3 font-semibold bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    {isUpdating ? 'Updating...' : 'Creating...'}
                  </>
                ) : isUpdating ? (
                  'Update Order'
                ) : (
                  'Create Order'
                )}
              </Button>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
